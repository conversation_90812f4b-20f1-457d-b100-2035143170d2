// This file is auto-generated by @hey-api/openapi-ts

import type { CancelablePromise } from './core/CancelablePromise';
import { OpenAPI } from './core/OpenAPI';
import { request as __request } from './core/request';
import type { AboutusCreateAboutUsData, AboutusCreateAboutUsResponse, AboutusReadAboutUsListData, AboutusReadAboutUsListResponse, AboutusEditAboutUsData, AboutusEditAboutUsResponse, AboutusDeleteAboutUsData, AboutusDeleteAboutUsResponse, AdminReadClientsData, AdminReadClientsResponse, AdminCreateClientAdminData, AdminCreateClientAdminResponse, AdminReadClientAdminData, AdminReadClientAdminResponse, AdminUpdateClientAdminData, AdminUpdateClientAdminResponse, AdminDeleteClientAdminData, AdminDeleteClientAdminResponse, BlogsCreateBlogData, BlogsCreateBlogResponse, BlogsGetBlogsData, BlogsGetBlogsResponse, BlogsEditBlogData, BlogsEditBlogResponse, BlogsGetBlogData, BlogsGetBlogResponse, BlogsDeleteBlogData, BlogsDeleteBlogResponse, BlogsDeleteBlogImagesData, BlogsDeleteBlogImagesResponse, ClientsClientLoginAccessTokenData, ClientsClientLoginAccessTokenResponse, ClientsRegisterClientData, ClientsRegisterClientResponse, ClientsVerifyEmailData, ClientsVerifyEmailResponse, ClientsReadClientMeResponse, ClientsUpdateClientMeData, ClientsUpdateClientMeResponse, IntroductionCreateIntroductionDocsData, IntroductionCreateIntroductionDocsResponse, IntroductionCreateIntroductionData, IntroductionCreateIntroductionResponse, IntroductionReadIntroductionListData, IntroductionReadIntroductionListResponse, IntroductionEditIntroductionData, IntroductionEditIntroductionResponse, IntroductionDeleteIntroductionData, IntroductionDeleteIntroductionResponse, ItemsReadItemsData, ItemsReadItemsResponse, ItemsCreateItemData, ItemsCreateItemResponse, ItemsReadItemData, ItemsReadItemResponse, ItemsUpdateItemData, ItemsUpdateItemResponse, ItemsDeleteItemData, ItemsDeleteItemResponse, LoginLoginAccessTokenData, LoginLoginAccessTokenResponse, LoginTestTokenResponse, LoginRecoverPasswordData, LoginRecoverPasswordResponse, LoginResetPasswordData, LoginResetPasswordResponse, LoginRecoverPasswordHtmlContentData, LoginRecoverPasswordHtmlContentResponse, MaCourseCreateMacourseData, MaCourseCreateMacourseResponse, MaCourseGetMacoursesData, MaCourseGetMacoursesResponse, MaCourseEditMacourseData, MaCourseEditMacourseResponse, MaCourseDeleteMacourseData, MaCourseDeleteMacourseResponse, PrivateCreateUserData, PrivateCreateUserResponse, QuestionnairesGetSurveyTypesResponse, QuestionnairesGetSurveyQuestionsData, QuestionnairesGetSurveyQuestionsResponse, QuestionnairesImportSurveyQuestionsJsonData, QuestionnairesImportSurveyQuestionsJsonResponse, QuestionnairesImportSurveyQuestionsData, QuestionnairesImportSurveyQuestionsResponse, QuestionnairesUpdateQuestionData, QuestionnairesUpdateQuestionResponse, QuestionnairesGetClientQuestionnairesData, QuestionnairesGetClientQuestionnairesResponse, QuestionnairesCreateClientQuestionnaireData, QuestionnairesCreateClientQuestionnaireResponse, QuestionnairesUpdateQuestionnaireStatusData, QuestionnairesUpdateQuestionnaireStatusResponse, QuestionnairesGetQuestionnaireData, QuestionnairesGetQuestionnaireResponse, QuestionnairesDeleteQuestionnaireData, QuestionnairesDeleteQuestionnaireResponse, QuestionnairesGetMyAvailableQuestionnaireResponse, QuestionnairesGetMyQuestionnairesResponse, QuestionnairesGetMyQuestionnaireDetailsData, QuestionnairesGetMyQuestionnaireDetailsResponse, QuestionnairesStartMyQuestionnaireData, QuestionnairesStartMyQuestionnaireResponse, QuestionnairesSubmitQuestionnaireAnswersData, QuestionnairesSubmitQuestionnaireAnswersResponse, QuestionnairesFinishMyQuestionnaireResponse, QuestionnairesGetPublicSurveyTypesResponse, QuestionnairesGetPublicSurveyQuestionsData, QuestionnairesGetPublicSurveyQuestionsResponse, SettingReadSettingResponse, SettingUpdateSettingData, SettingUpdateSettingResponse, UsersReadUsersData, UsersReadUsersResponse, UsersCreateUserData, UsersCreateUserResponse, UsersReadUserMeResponse, UsersDeleteUserMeResponse, UsersUpdateUserMeData, UsersUpdateUserMeResponse, UsersUpdatePasswordMeData, UsersUpdatePasswordMeResponse, UsersRegisterUserData, UsersRegisterUserResponse, UsersReadUserByIdData, UsersReadUserByIdResponse, UsersUpdateUserData, UsersUpdateUserResponse, UsersDeleteUserData, UsersDeleteUserResponse, UtilsTestEmailData, UtilsTestEmailResponse, UtilsHealthCheckResponse } from './types.gen';

export class AboutusService {
    /**
     * Create About Us
     * Create new about us.
     * @param data The data for the request.
     * @param data.formData
     * @returns AboutUs Successful Response
     * @throws ApiError
     */
    public static aboutusCreateAboutUs(data: AboutusCreateAboutUsData): CancelablePromise<AboutusCreateAboutUsResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/aboutus/',
            formData: data.formData,
            mediaType: 'multipart/form-data',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Read About Us List
     * @param data The data for the request.
     * @param data.skip
     * @param data.limit
     * @returns AboutUsPublic Successful Response
     * @throws ApiError
     */
    public static aboutusReadAboutUsList(data: AboutusReadAboutUsListData = {}): CancelablePromise<AboutusReadAboutUsListResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/aboutus/',
            query: {
                skip: data.skip,
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Edit About Us
     * @param data The data for the request.
     * @param data.id
     * @param data.formData
     * @returns AboutUs Successful Response
     * @throws ApiError
     */
    public static aboutusEditAboutUs(data: AboutusEditAboutUsData): CancelablePromise<AboutusEditAboutUsResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/aboutus/{id}',
            path: {
                id: data.id
            },
            formData: data.formData,
            mediaType: 'multipart/form-data',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Delete About Us
     * @param data The data for the request.
     * @param data.id
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static aboutusDeleteAboutUs(data: AboutusDeleteAboutUsData): CancelablePromise<AboutusDeleteAboutUsResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/aboutus/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class AdminService {
    /**
     * Read Clients
     * Retrieve clients with pagination, sorting, and filtering.
     * @param data The data for the request.
     * @param data.skip
     * @param data.limit
     * @param data.sortBy Field to sort by
     * @param data.sortOrder Sort order: asc or desc
     * @param data.statusFilter Filter by status: active, inactive, or all
     * @returns ClientsPublic Successful Response
     * @throws ApiError
     */
    public static adminReadClients(data: AdminReadClientsData = {}): CancelablePromise<AdminReadClientsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/admin_clients',
            query: {
                skip: data.skip,
                limit: data.limit,
                sort_by: data.sortBy,
                sort_order: data.sortOrder,
                status_filter: data.statusFilter
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Create Client Admin
     * Create new client (admin only).
     * @param data The data for the request.
     * @param data.requestBody
     * @returns ClientPublic Successful Response
     * @throws ApiError
     */
    public static adminCreateClientAdmin(data: AdminCreateClientAdminData): CancelablePromise<AdminCreateClientAdminResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/admin_clients',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Read Client Admin
     * Get a specific client by id (admin only).
     * @param data The data for the request.
     * @param data.clientId
     * @returns ClientPublic Successful Response
     * @throws ApiError
     */
    public static adminReadClientAdmin(data: AdminReadClientAdminData): CancelablePromise<AdminReadClientAdminResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/clients/{client_id}',
            path: {
                client_id: data.clientId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Update Client Admin
     * Update a client (admin only).
     * @param data The data for the request.
     * @param data.clientId
     * @param data.requestBody
     * @returns ClientPublic Successful Response
     * @throws ApiError
     */
    public static adminUpdateClientAdmin(data: AdminUpdateClientAdminData): CancelablePromise<AdminUpdateClientAdminResponse> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/api/v1/admin_clients/{client_id}',
            path: {
                client_id: data.clientId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Delete Client Admin
     * Delete a client (admin only).
     * @param data The data for the request.
     * @param data.clientId
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static adminDeleteClientAdmin(data: AdminDeleteClientAdminData): CancelablePromise<AdminDeleteClientAdminResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/admin_clients/{client_id}',
            path: {
                client_id: data.clientId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class BlogsService {
    /**
     * Create Blog
     * @param data The data for the request.
     * @param data.formData
     * @returns Blog Successful Response
     * @throws ApiError
     */
    public static blogsCreateBlog(data: BlogsCreateBlogData): CancelablePromise<BlogsCreateBlogResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/blogs/',
            formData: data.formData,
            mediaType: 'multipart/form-data',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get Blogs
     * @param data The data for the request.
     * @param data.skip
     * @param data.limit
     * @returns BlogsPublic Successful Response
     * @throws ApiError
     */
    public static blogsGetBlogs(data: BlogsGetBlogsData = {}): CancelablePromise<BlogsGetBlogsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/blogs/',
            query: {
                skip: data.skip,
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Edit Blog
     * @param data The data for the request.
     * @param data.id
     * @param data.formData
     * @returns Blog Successful Response
     * @throws ApiError
     */
    public static blogsEditBlog(data: BlogsEditBlogData): CancelablePromise<BlogsEditBlogResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/blogs/{id}',
            path: {
                id: data.id
            },
            formData: data.formData,
            mediaType: 'multipart/form-data',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get Blog
     * Get a specific blog by its ID.
     * @param data The data for the request.
     * @param data.id
     * @returns Blog Successful Response
     * @throws ApiError
     */
    public static blogsGetBlog(data: BlogsGetBlogData): CancelablePromise<BlogsGetBlogResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/blogs/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Delete Blog
     * Delete a blog.
     * @param data The data for the request.
     * @param data.id
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static blogsDeleteBlog(data: BlogsDeleteBlogData): CancelablePromise<BlogsDeleteBlogResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/blogs/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Delete Blog Images
     * @param data The data for the request.
     * @param data.id
     * @param data.formData
     * @returns Blog Successful Response
     * @throws ApiError
     */
    public static blogsDeleteBlogImages(data: BlogsDeleteBlogImagesData): CancelablePromise<BlogsDeleteBlogImagesResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/blogs/{id}/images',
            path: {
                id: data.id
            },
            formData: data.formData,
            mediaType: 'application/x-www-form-urlencoded',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class ClientsService {
    /**
     * Client Login Access Token
     * OAuth2 compatible token login, get an access token for future requests
     * @param data The data for the request.
     * @param data.formData
     * @returns Token Successful Response
     * @throws ApiError
     */
    public static clientsClientLoginAccessToken(data: ClientsClientLoginAccessTokenData): CancelablePromise<ClientsClientLoginAccessTokenResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/clients/login',
            formData: data.formData,
            mediaType: 'application/x-www-form-urlencoded',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Register Client
     * Create new client with email verification.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns ClientPublic Successful Response
     * @throws ApiError
     */
    public static clientsRegisterClient(data: ClientsRegisterClientData): CancelablePromise<ClientsRegisterClientResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/clients/signup',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Verify Email
     * Verify client email
     * @param data The data for the request.
     * @param data.token
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static clientsVerifyEmail(data: ClientsVerifyEmailData): CancelablePromise<ClientsVerifyEmailResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/clients/verify-email/',
            query: {
                token: data.token
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Read Client Me
     * Get current client profile.
     * @returns ClientPublic Successful Response
     * @throws ApiError
     */
    public static clientsReadClientMe(): CancelablePromise<ClientsReadClientMeResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/clients/me'
        });
    }
    
    /**
     * Update Client Me
     * Update current client profile.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns ClientPublic Successful Response
     * @throws ApiError
     */
    public static clientsUpdateClientMe(data: ClientsUpdateClientMeData): CancelablePromise<ClientsUpdateClientMeResponse> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/api/v1/clients/me',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class IntroductionService {
    /**
     * Create Introduction Docs
     * API Schema for documentation
     * @param data The data for the request.
     * @param data.requestBody
     * @returns Introduction Successful Response
     * @throws ApiError
     */
    public static introductionCreateIntroductionDocs(data: IntroductionCreateIntroductionDocsData): CancelablePromise<IntroductionCreateIntroductionDocsResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/introduction/docs',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Create Introduction
     * Create new about us.
     * @param data The data for the request.
     * @param data.formData
     * @returns Introduction Successful Response
     * @throws ApiError
     */
    public static introductionCreateIntroduction(data: IntroductionCreateIntroductionData): CancelablePromise<IntroductionCreateIntroductionResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/introduction/',
            formData: data.formData,
            mediaType: 'multipart/form-data',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Read Introduction List
     * @param data The data for the request.
     * @param data.skip
     * @param data.limit
     * @returns IntroductionsPublic Successful Response
     * @throws ApiError
     */
    public static introductionReadIntroductionList(data: IntroductionReadIntroductionListData = {}): CancelablePromise<IntroductionReadIntroductionListResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/introduction/',
            query: {
                skip: data.skip,
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Edit Introduction
     * @param data The data for the request.
     * @param data.id
     * @param data.formData
     * @returns Introduction Successful Response
     * @throws ApiError
     */
    public static introductionEditIntroduction(data: IntroductionEditIntroductionData): CancelablePromise<IntroductionEditIntroductionResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/introduction/{id}',
            path: {
                id: data.id
            },
            formData: data.formData,
            mediaType: 'multipart/form-data',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Delete Introduction
     * Delete an course.
     * @param data The data for the request.
     * @param data.id
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static introductionDeleteIntroduction(data: IntroductionDeleteIntroductionData): CancelablePromise<IntroductionDeleteIntroductionResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/introduction/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class ItemsService {
    /**
     * Read Items
     * Retrieve items.
     * @param data The data for the request.
     * @param data.skip
     * @param data.limit
     * @returns ItemsPublic Successful Response
     * @throws ApiError
     */
    public static itemsReadItems(data: ItemsReadItemsData = {}): CancelablePromise<ItemsReadItemsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/items/',
            query: {
                skip: data.skip,
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Create Item
     * Create new item.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns ItemPublic Successful Response
     * @throws ApiError
     */
    public static itemsCreateItem(data: ItemsCreateItemData): CancelablePromise<ItemsCreateItemResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/items/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Read Item
     * Get item by ID.
     * @param data The data for the request.
     * @param data.id
     * @returns ItemPublic Successful Response
     * @throws ApiError
     */
    public static itemsReadItem(data: ItemsReadItemData): CancelablePromise<ItemsReadItemResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/items/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Update Item
     * Update an item.
     * @param data The data for the request.
     * @param data.id
     * @param data.requestBody
     * @returns ItemPublic Successful Response
     * @throws ApiError
     */
    public static itemsUpdateItem(data: ItemsUpdateItemData): CancelablePromise<ItemsUpdateItemResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/items/{id}',
            path: {
                id: data.id
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Delete Item
     * Delete an item.
     * @param data The data for the request.
     * @param data.id
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static itemsDeleteItem(data: ItemsDeleteItemData): CancelablePromise<ItemsDeleteItemResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/items/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class LoginService {
    /**
     * Login Access Token
     * OAuth2 compatible token login, get an access token for future requests
     * @param data The data for the request.
     * @param data.formData
     * @returns Token Successful Response
     * @throws ApiError
     */
    public static loginLoginAccessToken(data: LoginLoginAccessTokenData): CancelablePromise<LoginLoginAccessTokenResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/login/access-token',
            formData: data.formData,
            mediaType: 'application/x-www-form-urlencoded',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Test Token
     * Test access token
     * @returns UserPublic Successful Response
     * @throws ApiError
     */
    public static loginTestToken(): CancelablePromise<LoginTestTokenResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/login/test-token'
        });
    }
    
    /**
     * Recover Password
     * Password Recovery
     * @param data The data for the request.
     * @param data.email
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static loginRecoverPassword(data: LoginRecoverPasswordData): CancelablePromise<LoginRecoverPasswordResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/password-recovery/{email}',
            path: {
                email: data.email
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Reset Password
     * Reset password
     * @param data The data for the request.
     * @param data.requestBody
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static loginResetPassword(data: LoginResetPasswordData): CancelablePromise<LoginResetPasswordResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/reset-password/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Recover Password Html Content
     * HTML Content for Password Recovery
     * @param data The data for the request.
     * @param data.email
     * @returns string Successful Response
     * @throws ApiError
     */
    public static loginRecoverPasswordHtmlContent(data: LoginRecoverPasswordHtmlContentData): CancelablePromise<LoginRecoverPasswordHtmlContentResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/password-recovery-html-content/{email}',
            path: {
                email: data.email
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class MaCourseService {
    /**
     * Create Macourse
     * @param data The data for the request.
     * @param data.formData
     * @returns MaCourseImage Successful Response
     * @throws ApiError
     */
    public static maCourseCreateMacourse(data: MaCourseCreateMacourseData): CancelablePromise<MaCourseCreateMacourseResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/ma_course/',
            formData: data.formData,
            mediaType: 'multipart/form-data',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get Macourses
     * @param data The data for the request.
     * @param data.skip
     * @param data.limit
     * @returns MaCourseImagesPublic Successful Response
     * @throws ApiError
     */
    public static maCourseGetMacourses(data: MaCourseGetMacoursesData = {}): CancelablePromise<MaCourseGetMacoursesResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/ma_course/',
            query: {
                skip: data.skip,
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Edit Macourse
     * Edit an existing MaCourse.
     * @param data The data for the request.
     * @param data.id
     * @param data.formData
     * @returns MaCourseImage Successful Response
     * @throws ApiError
     */
    public static maCourseEditMacourse(data: MaCourseEditMacourseData): CancelablePromise<MaCourseEditMacourseResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/ma_course/{id}',
            path: {
                id: data.id
            },
            formData: data.formData,
            mediaType: 'application/x-www-form-urlencoded',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Delete Macourse
     * Delete a MaCourse.
     * @param data The data for the request.
     * @param data.id
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static maCourseDeleteMacourse(data: MaCourseDeleteMacourseData): CancelablePromise<MaCourseDeleteMacourseResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/ma_course/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class PrivateService {
    /**
     * Create User
     * Create a new user.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns UserPublic Successful Response
     * @throws ApiError
     */
    public static privateCreateUser(data: PrivateCreateUserData): CancelablePromise<PrivateCreateUserResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/private/users/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class QuestionnairesService {
    /**
     * Get Survey Types
     * Get all survey types.
     * @returns SurveyTypePublic Successful Response
     * @throws ApiError
     */
    public static questionnairesGetSurveyTypes(): CancelablePromise<QuestionnairesGetSurveyTypesResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/questionnaires/survey-types'
        });
    }
    
    /**
     * Get Survey Questions
     * Get all questions for a specific survey type with details.
     * @param data The data for the request.
     * @param data.surveyTypeId
     * @returns SurveyQuestionsPublic Successful Response
     * @throws ApiError
     */
    public static questionnairesGetSurveyQuestions(data: QuestionnairesGetSurveyQuestionsData): CancelablePromise<QuestionnairesGetSurveyQuestionsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/questionnaires/survey/{survey_type_id}',
            path: {
                survey_type_id: data.surveyTypeId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Import Survey Questions Json
     * Import questions from JSON data. Deletes all existing questions for the survey type.
     * Expected format: {"questions": [{"intelligence_category": "...", "core_competency": "...", "question": "..."}]}
     *
     * Validation Rules:
     * - Must have exactly 8 different Intelligence Categories
     * - Each Intelligence Category must have exactly 4 different Core Competencies
     *
     * Returns 400 error with detailed message if validation fails.
     * @param data The data for the request.
     * @param data.surveyTypeId
     * @param data.requestBody
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static questionnairesImportSurveyQuestionsJson(data: QuestionnairesImportSurveyQuestionsJsonData): CancelablePromise<QuestionnairesImportSurveyQuestionsJsonResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/questionnaires/survey/{survey_type_id}/import-json',
            path: {
                survey_type_id: data.surveyTypeId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Import Survey Questions
     * Import questions from CSV file. Deletes all existing questions for the survey type.
     * Expected CSV headers: Intelligence Category, Core Competency, Question
     *
     * Validation Rules:
     * - Must have exactly 8 different Intelligence Categories
     * - Each Intelligence Category must have exactly 4 different Core Competencies
     *
     * Returns 400 error with detailed message if validation fails.
     * @param data The data for the request.
     * @param data.surveyTypeId
     * @param data.formData
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static questionnairesImportSurveyQuestions(data: QuestionnairesImportSurveyQuestionsData): CancelablePromise<QuestionnairesImportSurveyQuestionsResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/questionnaires/survey/{survey_type_id}/import',
            path: {
                survey_type_id: data.surveyTypeId
            },
            formData: data.formData,
            mediaType: 'multipart/form-data',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Update Question
     * Update a question. Only content and index can be updated.
     * @param data The data for the request.
     * @param data.questionId
     * @param data.requestBody
     * @returns QuestionPublic Successful Response
     * @throws ApiError
     */
    public static questionnairesUpdateQuestion(data: QuestionnairesUpdateQuestionData): CancelablePromise<QuestionnairesUpdateQuestionResponse> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/api/v1/questionnaires/question/{question_id}',
            path: {
                question_id: data.questionId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get Client Questionnaires
     * Get all questionnaires for a specific client (Admin only).
     * @param data The data for the request.
     * @param data.clientId
     * @returns QuestionnairesPublic Successful Response
     * @throws ApiError
     */
    public static questionnairesGetClientQuestionnaires(data: QuestionnairesGetClientQuestionnairesData): CancelablePromise<QuestionnairesGetClientQuestionnairesResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/questionnaires/clients/{client_id}/questionnaires',
            path: {
                client_id: data.clientId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Create Client Questionnaire
     * Create a new questionnaire for a client (Admin only).
     * Only one 'available' questionnaire per client is allowed.
     * @param data The data for the request.
     * @param data.clientId
     * @param data.requestBody
     * @returns QuestionnairePublic Successful Response
     * @throws ApiError
     */
    public static questionnairesCreateClientQuestionnaire(data: QuestionnairesCreateClientQuestionnaireData): CancelablePromise<QuestionnairesCreateClientQuestionnaireResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/questionnaires/clients/{client_id}/questionnaires',
            path: {
                client_id: data.clientId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Update Questionnaire Status
     * Update questionnaire status (Admin only).
     * @param data The data for the request.
     * @param data.questionnaireId
     * @param data.status
     * @returns QuestionnairePublic Successful Response
     * @throws ApiError
     */
    public static questionnairesUpdateQuestionnaireStatus(data: QuestionnairesUpdateQuestionnaireStatusData): CancelablePromise<QuestionnairesUpdateQuestionnaireStatusResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/questionnaires/questionnaires/{questionnaire_id}/status',
            path: {
                questionnaire_id: data.questionnaireId
            },
            query: {
                status: data.status
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get Questionnaire
     * Get detailed information about a specific questionnaire (Admin only).
     * @param data The data for the request.
     * @param data.questionnaireId
     * @returns QuestionnaireWithDetails Successful Response
     * @throws ApiError
     */
    public static questionnairesGetQuestionnaire(data: QuestionnairesGetQuestionnaireData): CancelablePromise<QuestionnairesGetQuestionnaireResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/questionnaires/questionnaires/{questionnaire_id}',
            path: {
                questionnaire_id: data.questionnaireId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Delete Questionnaire
     * Delete a questionnaire (Admin only).
     * @param data The data for the request.
     * @param data.questionnaireId
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static questionnairesDeleteQuestionnaire(data: QuestionnairesDeleteQuestionnaireData): CancelablePromise<QuestionnairesDeleteQuestionnaireResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/questionnaires/questionnaires/{questionnaire_id}',
            path: {
                questionnaire_id: data.questionnaireId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get My Available Questionnaire
     * Get the available questionnaire for the current client.
     * Returns None if no available questionnaire exists.
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static questionnairesGetMyAvailableQuestionnaire(): CancelablePromise<QuestionnairesGetMyAvailableQuestionnaireResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/questionnaires/me/questionnaire'
        });
    }
    
    /**
     * Get My Questionnaires
     * Get all questionnaires for the current client (both available and finished).
     * @returns QuestionnairesPublic Successful Response
     * @throws ApiError
     */
    public static questionnairesGetMyQuestionnaires(): CancelablePromise<QuestionnairesGetMyQuestionnairesResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/questionnaires/me/questionnaires'
        });
    }
    
    /**
     * Get My Questionnaire Details
     * Get detailed information about a specific questionnaire for the current client.
     * @param data The data for the request.
     * @param data.questionnaireId
     * @returns QuestionnaireWithDetails Successful Response
     * @throws ApiError
     */
    public static questionnairesGetMyQuestionnaireDetails(data: QuestionnairesGetMyQuestionnaireDetailsData): CancelablePromise<QuestionnairesGetMyQuestionnaireDetailsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/questionnaires/me/questionnaires/{questionnaire_id}',
            path: {
                questionnaire_id: data.questionnaireId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Start My Questionnaire
     * Start filling out the questionnaire by saving basic info and survey type.
     * Updates the existing available questionnaire for the client.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns QuestionnairePublic Successful Response
     * @throws ApiError
     */
    public static questionnairesStartMyQuestionnaire(data: QuestionnairesStartMyQuestionnaireData): CancelablePromise<QuestionnairesStartMyQuestionnaireResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/questionnaires/me/questionnaire/start',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Submit Questionnaire Answers
     * Submit answers for the questionnaire.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns QuestionnairePublic Successful Response
     * @throws ApiError
     */
    public static questionnairesSubmitQuestionnaireAnswers(data: QuestionnairesSubmitQuestionnaireAnswersData): CancelablePromise<QuestionnairesSubmitQuestionnaireAnswersResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/questionnaires/me/questionnaire/answers',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Finish My Questionnaire
     * Mark the questionnaire as finished.
     * @returns QuestionnairePublic Successful Response
     * @throws ApiError
     */
    public static questionnairesFinishMyQuestionnaire(): CancelablePromise<QuestionnairesFinishMyQuestionnaireResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/questionnaires/me/questionnaire/finish'
        });
    }
    
    /**
     * Get Public Survey Types
     * Get all survey types (public endpoint for client-side).
     * @returns SurveyTypePublic Successful Response
     * @throws ApiError
     */
    public static questionnairesGetPublicSurveyTypes(): CancelablePromise<QuestionnairesGetPublicSurveyTypesResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/questionnaires/public/survey-types'
        });
    }
    
    /**
     * Get Public Survey Questions
     * Get all questions for a specific survey type with details (public client-side endpoint).
     * @param data The data for the request.
     * @param data.surveyTypeId
     * @returns SurveyQuestionsPublic Successful Response
     * @throws ApiError
     */
    public static questionnairesGetPublicSurveyQuestions(data: QuestionnairesGetPublicSurveyQuestionsData): CancelablePromise<QuestionnairesGetPublicSurveyQuestionsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/questionnaires/public/survey/{survey_type_id}/questions',
            path: {
                survey_type_id: data.surveyTypeId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class SettingService {
    /**
     * Read Setting
     * Retrieve users.
     * @returns Setting Successful Response
     * @throws ApiError
     */
    public static settingReadSetting(): CancelablePromise<SettingReadSettingResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/setting/'
        });
    }
    
    /**
     * Update Setting
     * Update an item.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns SettingBase Successful Response
     * @throws ApiError
     */
    public static settingUpdateSetting(data: SettingUpdateSettingData): CancelablePromise<SettingUpdateSettingResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/setting/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class UsersService {
    /**
     * Read Users
     * Retrieve users.
     * @param data The data for the request.
     * @param data.skip
     * @param data.limit
     * @returns UsersPublic Successful Response
     * @throws ApiError
     */
    public static usersReadUsers(data: UsersReadUsersData = {}): CancelablePromise<UsersReadUsersResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/users/',
            query: {
                skip: data.skip,
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Create User
     * Create new user.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns UserPublic Successful Response
     * @throws ApiError
     */
    public static usersCreateUser(data: UsersCreateUserData): CancelablePromise<UsersCreateUserResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/users/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Read User Me
     * Get current user.
     * @returns UserPublic Successful Response
     * @throws ApiError
     */
    public static usersReadUserMe(): CancelablePromise<UsersReadUserMeResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/users/me'
        });
    }
    
    /**
     * Delete User Me
     * Delete own user.
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static usersDeleteUserMe(): CancelablePromise<UsersDeleteUserMeResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/users/me'
        });
    }
    
    /**
     * Update User Me
     * Update own user.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns UserPublic Successful Response
     * @throws ApiError
     */
    public static usersUpdateUserMe(data: UsersUpdateUserMeData): CancelablePromise<UsersUpdateUserMeResponse> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/api/v1/users/me',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Update Password Me
     * Update own password.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static usersUpdatePasswordMe(data: UsersUpdatePasswordMeData): CancelablePromise<UsersUpdatePasswordMeResponse> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/api/v1/users/me/password',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Register User
     * Create new user without the need to be logged in.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns UserPublic Successful Response
     * @throws ApiError
     */
    public static usersRegisterUser(data: UsersRegisterUserData): CancelablePromise<UsersRegisterUserResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/users/signup',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Read User By Id
     * Get a specific user by id.
     * @param data The data for the request.
     * @param data.userId
     * @returns UserPublic Successful Response
     * @throws ApiError
     */
    public static usersReadUserById(data: UsersReadUserByIdData): CancelablePromise<UsersReadUserByIdResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/users/{user_id}',
            path: {
                user_id: data.userId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Update User
     * Update a user.
     * @param data The data for the request.
     * @param data.userId
     * @param data.requestBody
     * @returns UserPublic Successful Response
     * @throws ApiError
     */
    public static usersUpdateUser(data: UsersUpdateUserData): CancelablePromise<UsersUpdateUserResponse> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/api/v1/users/{user_id}',
            path: {
                user_id: data.userId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Delete User
     * Delete a user.
     * @param data The data for the request.
     * @param data.userId
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static usersDeleteUser(data: UsersDeleteUserData): CancelablePromise<UsersDeleteUserResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/users/{user_id}',
            path: {
                user_id: data.userId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class UtilsService {
    /**
     * Test Email
     * Test emails.
     * @param data The data for the request.
     * @param data.emailTo
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static utilsTestEmail(data: UtilsTestEmailData): CancelablePromise<UtilsTestEmailResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/utils/test-email/',
            query: {
                email_to: data.emailTo
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Health Check
     * @returns boolean Successful Response
     * @throws ApiError
     */
    public static utilsHealthCheck(): CancelablePromise<UtilsHealthCheckResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/utils/health-check/'
        });
    }
    
}