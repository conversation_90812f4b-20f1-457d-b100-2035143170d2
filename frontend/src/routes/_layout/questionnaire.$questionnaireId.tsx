import {
  <PERSON>,
  Container,
  Flex,
  <PERSON>ing,
  <PERSON>,
  <PERSON>ge,
  Button,
  VStack,
  HStack,
  Stack,
  IconButton,
  Grid,
  GridItem,
  Accordion,
  Separator,
} from "@chakra-ui/react"
import { useQuery } from "@tanstack/react-query"
import { createFileRoute, useParams, useNavigate } from "@tanstack/react-router"
import { FiArrowLeft, FiPrinter } from "react-icons/fi"
import { format } from "date-fns"
import { QuestionnairesService } from "@/client"
import QuestionnaireStatusBadge from "@/components/Questionnaires/QuestionnaireStatusBadge"

export const Route = createFileRoute("/_layout/questionnaire/$questionnaireId")({
  component: QuestionnaireDetailsPage,
})

// A4 Size Box Component
// A4 dimensions: 210mm × 297mm ≈ 794px × 1123px at 96 DPI
const A4Box = ({ children, ...props }: { children: React.ReactNode } & any) => (
  <Box
    width="794px"
    minHeight="1123px"
    maxWidth="100%"
    mx="auto"
    bg="white"
    border="1px solid #e0e0e0"
    borderRadius="8px"
    boxShadow="0 4px 12px rgba(0, 0, 0, 0.1)"
    p={6}
    position="relative"
    {...props}
  >
    {children}
  </Box>
)

// Helper function to get survey type display name
const getSurveyTypeDisplayName = (surveyTypeName: string | null) => {
  if (!surveyTypeName) return "Unknown Assessment Type"

  switch (surveyTypeName) {
    case "3-6 years questionnaire":
      return "3-6 Years"
    case "7-11 years questionnaire":
      return "7-11 Years"
    case "12-15 years questionnaire":
      return "12-15 Years"
    default:
      return surveyTypeName
  }
}

// Helper function to format date
const formatDate = (dateString: string) => {
  try {
    return format(new Date(dateString), "MMM dd, yyyy 'at' HH:mm")
  } catch {
    return "Unknown date"
  }
}

// Helper function to get answer option labels
const getAnswerLabel = (answer: string) => {
  const labels: Record<string, string> = {
    A: "Performs significantly better than described (or shows exceptional talent)",
    B: "Completely matches the description",
    C: "Mostly matches the description",
    D: "Partially matches the description",
    E: "Rarely matches the description",
    F: "Does not match the description at all",
  }
  return labels[answer] || `Option ${answer}`
}

function QuestionnaireDetailsPage() {
  const navigate = useNavigate()
  const { questionnaireId } = useParams({ strict: false })

  // Get questionnaire details
  const { data: questionnaire, isLoading, error } = useQuery({
    queryKey: ["questionnaire-details", questionnaireId],
    queryFn: () => QuestionnairesService.questionnairesGetQuestionnaire({
      questionnaireId: questionnaireId || ""
    }),
    enabled: !!questionnaireId,
  })

  const handleBack = () => {
    // Navigate back to the client details page if we have client_id
    if (questionnaire?.client_id) {
      navigate({ to: "/client/$clientId", params: { clientId: questionnaire.client_id } })
    } else {
      navigate({ to: "/clients" })
    }
  }

  if (isLoading) {
    return <QuestionnaireDetailsSkeleton />
  }

  if (error || !questionnaire) {
    return (
      <Container maxW="full">
        <VStack gap={6} py={8}>
          <Text fontSize="lg" color="red.500">
            {error ? "Error loading questionnaire details" : "Questionnaire not found"}
          </Text>
          <Button onClick={handleBack}>
            <FiArrowLeft />
            Back
          </Button>
        </VStack>
      </Container>
    )
  }

  // Extract answers from questionnaire data
  const answers = (questionnaire.questions_and_ans?.answers || []) as any[]

  // Group answers by intelligence category
  const answersByCategory = answers.reduce((acc: Record<string, any[]>, answer: any) => {
    const category = answer.intelligence_category_name || "Other"
    if (!acc[category]) {
      acc[category] = []
    }
    acc[category].push(answer)
    return acc
  }, {} as Record<string, any[]>)

  return (
    <Container maxW="full">
      <VStack gap={6} py={8} align="stretch">
        {/* Header */}
        <Flex justify="space-between" align="center">
          <HStack gap={4}>
            <IconButton
              aria-label="Back"
              onClick={handleBack}
              variant="outline"
              size="sm"
            >
              <FiArrowLeft />
            </IconButton>
            <Heading size="lg">Questionnaire Details</Heading>
          </HStack>
          <Button
            colorScheme="blue"
            size="sm"
            onClick={() => window.print()}
          >
            <FiPrinter />
            Print Details
          </Button>
        </Flex>

        {/* Basic Information Card */}
        <Box
          bg="white"
          p={6}
          borderRadius="lg"
          shadow="sm"
          border="1px"
          borderColor="gray.200"
        >
          <VStack gap={6} align="stretch">
            <Heading size="md" color="gray.700">
              Basic Information
            </Heading>

            <Grid templateColumns="repeat(auto-fit, minmax(300px, 1fr))" gap={6}>
              <GridItem>
                <Stack gap={4}>
                  <Flex justify="space-between" align="center">
                    <Text fontWeight="medium" color="gray.600">
                      Child Name:
                    </Text>
                    <Text fontSize="lg" fontWeight="semibold">
                      {questionnaire.name}
                    </Text>
                  </Flex>

                  <Flex justify="space-between" align="center">
                    <Text fontWeight="medium" color="gray.600">
                      Assessment Type:
                    </Text>
                    <Text fontSize="lg">
                      {getSurveyTypeDisplayName(questionnaire.survey_type_name)} Assessment
                    </Text>
                  </Flex>

                  {questionnaire.birth_info && (
                    <Flex justify="space-between" align="center">
                      <Text fontWeight="medium" color="gray.600">
                        Birth Info:
                      </Text>
                      <Text fontSize="lg">
                        {questionnaire.birth_info}
                      </Text>
                    </Flex>
                  )}

                  {questionnaire.grade_level && (
                    <Flex justify="space-between" align="center">
                      <Text fontWeight="medium" color="gray.600">
                        Grade Level:
                      </Text>
                      <Text fontSize="lg">
                        {questionnaire.grade_level}
                      </Text>
                    </Flex>
                  )}
                </Stack>
              </GridItem>

              <GridItem>
                <Stack gap={4}>
                  {questionnaire.caregiver && (
                    <Flex justify="space-between" align="center">
                      <Text fontWeight="medium" color="gray.600">
                        Caregiver:
                      </Text>
                      <Text fontSize="lg">
                        {questionnaire.caregiver}
                      </Text>
                    </Flex>
                  )}

                  {questionnaire.feeding && (
                    <Flex justify="space-between" align="center">
                      <Text fontWeight="medium" color="gray.600">
                        Feeding Method:
                      </Text>
                      <Text fontSize="lg">
                        {questionnaire.feeding}
                      </Text>
                    </Flex>
                  )}

                  {questionnaire.native_language && (
                    <Flex justify="space-between" align="center">
                      <Text fontWeight="medium" color="gray.600">
                        Native Language:
                      </Text>
                      <Text fontSize="lg">
                        {questionnaire.native_language}
                      </Text>
                    </Flex>
                  )}

                  <Flex justify="space-between" align="center">
                    <Text fontWeight="medium" color="gray.600">
                      Status:
                    </Text>
                    <QuestionnaireStatusBadge status={questionnaire.status} />
                  </Flex>
                </Stack>
              </GridItem>

              <GridItem>
                <Stack gap={4}>
                  <Flex justify="space-between" align="center">
                    <Text fontWeight="medium" color="gray.600">
                      Created:
                    </Text>
                    <Text fontSize="lg">
                      {formatDate(questionnaire.created_at)}
                    </Text>
                  </Flex>

                  {questionnaire.finished_at && (
                    <Flex justify="space-between" align="center">
                      <Text fontWeight="medium" color="gray.600">
                        Finished:
                      </Text>
                      <Text fontSize="lg">
                        {formatDate(questionnaire.finished_at)}
                      </Text>
                    </Flex>
                  )}

                  <Flex justify="space-between" align="center">
                    <Text fontWeight="medium" color="gray.600">
                      Total Questions:
                    </Text>
                    <Text fontSize="lg" fontWeight="semibold">
                      {answers.length} questions
                    </Text>
                  </Flex>
                </Stack>
              </GridItem>
            </Grid>
          </VStack>
        </Box>

        {/* A4 Report Section */}
        <A4Box>
          <VStack gap={6} align="stretch">
            {/* Report Header */}
            <Box textAlign="center" borderBottom="2px solid" borderColor="gray.300" pb={4}>
              <Heading size="lg" color="gray.800" fontFamily="Inter">
                智能核心評估報告
              </Heading>
              <Text fontSize="md" color="gray.600" mt={2}>
                Intelligence Core Assessment Report
              </Text>
            </Box>

            {/* Report Content - Straight List Format */}
            <VStack gap={4} align="stretch" pt={4}>
              {/* Survey Type ID */}
              <Flex justify="space-between" align="center" py={2} borderBottom="1px solid" borderColor="gray.100">
                <Text fontSize="md" fontWeight="medium" color="gray.700" minW="200px">
                  Survey Type ID:
                </Text>
                <Text fontSize="md" color="gray.900" textAlign="right">
                  {questionnaire.survey_type_id || "N/A"}
                </Text>
              </Flex>

              {/* Survey Type Name */}
              <Flex justify="space-between" align="center" py={2} borderBottom="1px solid" borderColor="gray.100">
                <Text fontSize="md" fontWeight="medium" color="gray.700" minW="200px">
                  Survey Type:
                </Text>
                <Text fontSize="md" color="gray.900" textAlign="right">
                  {getSurveyTypeDisplayName(questionnaire.survey_type_name)}
                </Text>
              </Flex>

              {/* Child Name */}
              <Flex justify="space-between" align="center" py={2} borderBottom="1px solid" borderColor="gray.100">
                <Text fontSize="md" fontWeight="medium" color="gray.700" minW="200px">
                  Child Name:
                </Text>
                <Text fontSize="md" color="gray.900" textAlign="right" fontWeight="semibold">
                  {questionnaire.name || "N/A"}
                </Text>
              </Flex>

              {/* Birth Info */}
              <Flex justify="space-between" align="center" py={2} borderBottom="1px solid" borderColor="gray.100">
                <Text fontSize="md" fontWeight="medium" color="gray.700" minW="200px">
                  Birth Information:
                </Text>
                <Text fontSize="md" color="gray.900" textAlign="right">
                  {questionnaire.birth_info || "N/A"}
                </Text>
              </Flex>

              {/* Caregiver */}
              <Flex justify="space-between" align="center" py={2} borderBottom="1px solid" borderColor="gray.100">
                <Text fontSize="md" fontWeight="medium" color="gray.700" minW="200px">
                  Caregiver:
                </Text>
                <Text fontSize="md" color="gray.900" textAlign="right">
                  {questionnaire.caregiver || "N/A"}
                </Text>
              </Flex>

              {/* Feeding Method */}
              <Flex justify="space-between" align="center" py={2} borderBottom="1px solid" borderColor="gray.100">
                <Text fontSize="md" fontWeight="medium" color="gray.700" minW="200px">
                  Feeding Method:
                </Text>
                <Text fontSize="md" color="gray.900" textAlign="right">
                  {questionnaire.feeding || "N/A"}
                </Text>
              </Flex>

              {/* Native Language */}
              <Flex justify="space-between" align="center" py={2} borderBottom="1px solid" borderColor="gray.100">
                <Text fontSize="md" fontWeight="medium" color="gray.700" minW="200px">
                  Native Language:
                </Text>
                <Text fontSize="md" color="gray.900" textAlign="right">
                  {questionnaire.native_language || "N/A"}
                </Text>
              </Flex>

              {/* Grade Level */}
              <Flex justify="space-between" align="center" py={2} borderBottom="1px solid" borderColor="gray.100">
                <Text fontSize="md" fontWeight="medium" color="gray.700" minW="200px">
                  Grade Level:
                </Text>
                <Text fontSize="md" color="gray.900" textAlign="right">
                  {questionnaire.grade_level || "N/A"}
                </Text>
              </Flex>
            </VStack>

            {/* Report Footer */}
            <Box textAlign="center" pt={8} mt={8} borderTop="1px solid" borderColor="gray.200">
              <Text fontSize="sm" color="gray.500">
                Generated on {formatDate(new Date().toISOString())}
              </Text>
            </Box>
          </VStack>
        </A4Box>

        {/* Questions and Answers Section */}
        <Box
          bg="white"
          p={6}
          borderRadius="lg"
          shadow="sm"
          border="1px"
          borderColor="gray.200"
        >
          <VStack gap={6} align="stretch">
            <Heading size="md" color="gray.700">
              Questions & Answers
            </Heading>

            {Object.keys(answersByCategory).length === 0 ? (
              <Text color="gray.500" textAlign="center" py={8}>
                No answers recorded yet.
              </Text>
            ) : (
              <Accordion.Root multiple>
                {Object.entries(answersByCategory).map(([category, categoryAnswers]) => (
                  <Accordion.Item key={category} value={category}>
                    <Accordion.ItemTrigger
                      bg="gray.50"
                      _hover={{ bg: "gray.100" }}
                      borderRadius="md"
                      p={4}
                      mb={2}
                    >
                      <Box flex="1" textAlign="left">
                        <Text fontSize="lg" fontWeight="bold" color="gray.800">
                          {category} ({categoryAnswers.length} questions)
                        </Text>
                      </Box>
                      <Accordion.ItemIndicator />
                    </Accordion.ItemTrigger>
                    <Accordion.ItemContent>
                      <Accordion.ItemBody p={4}>
                        <VStack gap={6} align="stretch">
                          {categoryAnswers.map((answer: any, index: number) => (
                            <Box key={index} p={4} border="1px" borderColor="gray.200" borderRadius="md" bg="gray.50">
                              <VStack align="flex-start" gap={4}>
                                <Text fontSize="md" fontWeight="bold" color="gray.800">
                                  Question {answer.question_id}
                                </Text>

                                <Text fontSize="sm" color="gray.700" lineHeight="1.6">
                                  {answer.question_text}
                                </Text>

                                <Separator />

                                <HStack align="flex-start" gap={2}>
                                  <Text fontSize="sm" color="gray.600" fontWeight="medium">
                                    Core Competency:
                                  </Text>
                                  <Text fontSize="sm" color="gray.800">
                                    {answer.core_competency_name}
                                  </Text>
                                </HStack>

                                <HStack align="flex-start" gap={2}>
                                  <Text fontSize="sm" color="gray.600" fontWeight="medium">
                                    Selected Answer:
                                  </Text>
                                  <Badge colorScheme="blue" fontSize="xs">
                                    {answer.selected_answer}
                                  </Badge>
                                </HStack>

                                <Box pl={4} borderLeft="3px solid" borderColor="blue.400">
                                  <Text fontSize="sm" color="gray.700" lineHeight="1.5">
                                    {getAnswerLabel(answer.selected_answer)}
                                  </Text>
                                </Box>
                              </VStack>
                            </Box>
                          ))}
                        </VStack>
                      </Accordion.ItemBody>
                    </Accordion.ItemContent>
                  </Accordion.Item>
                ))}
              </Accordion.Root>
            )}
          </VStack>
        </Box>
      </VStack>
    </Container>
  )
}

function QuestionnaireDetailsSkeleton() {
  return (
    <Container maxW="full">
      <VStack gap={6} py={8} align="stretch">
        {/* Header Skeleton */}
        <Flex justify="space-between" align="center">
          <HStack gap={4}>
            <Box w={10} h={10} bg="gray.200" borderRadius="md" />
            <Box w={48} h={8} bg="gray.200" borderRadius="md" />
          </HStack>
          <Box w={32} h={10} bg="gray.200" borderRadius="md" />
        </Flex>

        {/* Content Skeleton */}
        <Box
          bg="white"
          p={6}
          borderRadius="lg"
          shadow="sm"
          border="1px"
          borderColor="gray.200"
        >
          <VStack gap={6} align="stretch">
            <Box w={48} h={6} bg="gray.200" borderRadius="md" />
            <Grid templateColumns="repeat(auto-fit, minmax(300px, 1fr))" gap={6}>
              {Array.from({ length: 3 }).map((_, i) => (
                <GridItem key={i}>
                  <Stack gap={4}>
                    {Array.from({ length: 4 }).map((_, j) => (
                      <Flex key={j} justify="space-between" align="center">
                        <Box w={24} h={5} bg="gray.200" borderRadius="md" />
                        <Box w={32} h={5} bg="gray.200" borderRadius="md" />
                      </Flex>
                    ))}
                  </Stack>
                </GridItem>
              ))}
            </Grid>
          </VStack>
        </Box>
      </VStack>
    </Container>
  )
}
