import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  VStack,
  HStack,
  Grid,
  Grid<PERSON>tem,
  Card,
  <PERSON>ing,
  Separator,
  Badge,
  Accordion,
} from "@chakra-ui/react"
import { useQuery } from "@tanstack/react-query"
import { createFileRoute, useNavigate, useParams } from "@tanstack/react-router"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"
// Simple arrow left icon component
const ArrowLeftIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
    <path d="M15 18L9 12L15 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

// A4 Size Box Component
// A4 dimensions: 210mm × 297mm ≈ 794px × 1123px at 96 DPI
const A4Box = ({ children, ...props }: { children: React.ReactNode } & any) => (
  <Box
    width="794px"
    minHeight="1123px"
    maxWidth="100%"
    mx="auto"
    bg="white"
    border="1px solid #e0e0e0"
    borderRadius="8px"
    boxShadow="0 4px 12px rgba(0, 0, 0, 0.1)"
    p={6}
    position="relative"
    {...props}
  >
    {children}
  </Box>
)

import { QuestionnairesService } from "@/client"
import Header from "@/components/header"
import { ChineseText } from "@/components/ui/fonts"

export const Route = createFileRoute("/_layout/questionnaire-details/$questionnaireId")({
  component: QuestionnaireDetailsPage,
})

// Helper function to get survey type display name
const getSurveyTypeDisplayName = (surveyTypeName: string | null) => {
  if (!surveyTypeName) return "未知評估類型"
  
  switch (surveyTypeName) {
    case "3-6 years questionnaire":
      return "3-6歲"
    case "7-11 years questionnaire":
      return "7-11歲"
    case "12-15 years questionnaire":
      return "12-15歲"
    default:
      return surveyTypeName
  }
}

// Helper function to format date
const formatDate = (dateString: string) => {
  try {
    return format(new Date(dateString), "yyyy年MM月dd日 HH:mm", { locale: zhCN })
  } catch {
    return "未知日期"
  }
}

// Helper function to get answer option labels
const getAnswerLabel = (answer: string) => {
  const labels: Record<string, string> = {
    A: "實際情況相比題目描述表現得更出色（或表現出超高的天份）",
    B: "與實際情況比較，小朋友的情況完全符合題目的描述",
    C: "與實際情況比較，小朋友的情況大部份符合題目的描述",
    D: "與實際情況比較，小朋友的情況小部份符合題目的描述",
    E: "與實際情況比較，小朋友的情況基本不份符合題目的描述",
    F: "與實際情況比較，小朋友的情況完全不份符合題目的描述",
  }
  return labels[answer] || `選項 ${answer}`
}

function QuestionnaireDetailsPage() {
  const navigate = useNavigate()
  const { questionnaireId } = useParams({ strict: false })

  // Get questionnaire details
  const { data: questionnaire, isLoading, error } = useQuery({
    queryKey: ["questionnaire-details", questionnaireId],
    queryFn: () => QuestionnairesService.getMyQuestionnaireDetails({
      questionnaireId: questionnaireId || ""
    }),
    enabled: !!questionnaireId,
  })

  if (isLoading) {
    return (
      <Box bg="#f9f5e9" minH="100vh" display="flex" alignItems="center" justifyContent="center">
        <VStack gap={4}>
          <Text fontSize="24px" fontFamily="Inter">載入問卷詳情中...</Text>
        </VStack>
      </Box>
    )
  }

  if (error || !questionnaire) {
    return (
      <Box bg="#f9f5e9" minH="100vh" display="flex" alignItems="center" justifyContent="center">
        <VStack gap={4}>
          <Text fontSize="24px" fontFamily="Inter" color="red.500">問卷不存在或載入失敗</Text>
          <Button onClick={() => navigate({ to: "/questionnaire-history" })}>返回問卷記錄</Button>
        </VStack>
      </Box>
    )
  }

  // Extract answers from questionnaire data
  const answers = (questionnaire.questions_and_ans?.answers || []) as any[]

  // Group answers by intelligence category
  const answersByCategory = answers.reduce((acc: Record<string, any[]>, answer: any) => {
    const category = answer.intelligence_category_name || "其他"
    if (!acc[category]) {
      acc[category] = []
    }
    acc[category].push(answer)
    return acc
  }, {} as Record<string, any[]>)

  return (
    <Box bg="#f9f5e9" minH="100vh">
      <Header title="問卷詳情" />
      
      <Box maxW="1200px" mx="auto" px={8} py={8}>
        <VStack gap={8} align="stretch">
          {/* Back Button */}
          <HStack>
            <Button
              variant="ghost"
              onClick={() => navigate({ to: "/questionnaire-history" })}
              color="#666666"
              _hover={{ bg: "rgba(0,0,0,0.05)" }}
            >
              <ArrowLeftIcon />
              <ChineseText fontFamily="Inter" ml={2}>返回問卷記錄</ChineseText>
            </Button>
          </HStack>

          {/* Basic Information Card */}
          <Card.Root border="1px solid #e0e0e0" borderRadius="12px">
            <Card.Header>
              <Heading size="md" color="#000000" fontFamily="Inter">
                基本資訊
              </Heading>
            </Card.Header>
            <Card.Body>
              <Grid templateColumns="repeat(auto-fit, minmax(250px, 1fr))" gap={4}>
                <GridItem>
                  <VStack align="flex-start" gap={2}>
                    <HStack>
                      <ChineseText fontSize="14px" color="#666666" fontFamily="Inter" fontWeight="bold">
                        孩子姓名：
                      </ChineseText>
                      <ChineseText fontSize="14px" color="#000000" fontFamily="Inter">
                        {questionnaire.name}
                      </ChineseText>
                    </HStack>
                    
                    <HStack>
                      <ChineseText fontSize="14px" color="#666666" fontFamily="Inter" fontWeight="bold">
                        評估類型：
                      </ChineseText>
                      <ChineseText fontSize="14px" color="#000000" fontFamily="Inter">
                        {getSurveyTypeDisplayName(questionnaire.survey_type_name)}智能核心評估
                      </ChineseText>
                    </HStack>

                    {questionnaire.birth_info && (
                      <HStack>
                        <ChineseText fontSize="14px" color="#666666" fontFamily="Inter" fontWeight="bold">
                          出生資訊：
                        </ChineseText>
                        <ChineseText fontSize="14px" color="#000000" fontFamily="Inter">
                          {questionnaire.birth_info}
                        </ChineseText>
                      </HStack>
                    )}

                    {questionnaire.grade_level && (
                      <HStack>
                        <ChineseText fontSize="14px" color="#666666" fontFamily="Inter" fontWeight="bold">
                          年級：
                        </ChineseText>
                        <ChineseText fontSize="14px" color="#000000" fontFamily="Inter">
                          {questionnaire.grade_level}
                        </ChineseText>
                      </HStack>
                    )}
                  </VStack>
                </GridItem>

                <GridItem>
                  <VStack align="flex-start" gap={2}>
                    {questionnaire.caregiver && (
                      <HStack>
                        <ChineseText fontSize="14px" color="#666666" fontFamily="Inter" fontWeight="bold">
                          照顧者：
                        </ChineseText>
                        <ChineseText fontSize="14px" color="#000000" fontFamily="Inter">
                          {questionnaire.caregiver}
                        </ChineseText>
                      </HStack>
                    )}

                    {questionnaire.feeding && (
                      <HStack>
                        <ChineseText fontSize="14px" color="#666666" fontFamily="Inter" fontWeight="bold">
                          餵養方式：
                        </ChineseText>
                        <ChineseText fontSize="14px" color="#000000" fontFamily="Inter">
                          {questionnaire.feeding}
                        </ChineseText>
                      </HStack>
                    )}

                    {questionnaire.native_language && (
                      <HStack>
                        <ChineseText fontSize="14px" color="#666666" fontFamily="Inter" fontWeight="bold">
                          母語：
                        </ChineseText>
                        <ChineseText fontSize="14px" color="#000000" fontFamily="Inter">
                          {questionnaire.native_language}
                        </ChineseText>
                      </HStack>
                    )}

                    <HStack>
                      <ChineseText fontSize="14px" color="#666666" fontFamily="Inter" fontWeight="bold">
                        狀態：
                      </ChineseText>
                      <Badge colorScheme="green" fontSize="12px">
                        已完成
                      </Badge>
                    </HStack>
                  </VStack>
                </GridItem>

                <GridItem>
                  <VStack align="flex-start" gap={2}>
                    <HStack>
                      <ChineseText fontSize="14px" color="#666666" fontFamily="Inter" fontWeight="bold">
                        開始時間：
                      </ChineseText>
                      <ChineseText fontSize="14px" color="#000000" fontFamily="Inter">
                        {formatDate(questionnaire.created_at)}
                      </ChineseText>
                    </HStack>

                    {questionnaire.finished_at && (
                      <HStack>
                        <ChineseText fontSize="14px" color="#666666" fontFamily="Inter" fontWeight="bold">
                          完成時間：
                        </ChineseText>
                        <ChineseText fontSize="14px" color="#000000" fontFamily="Inter">
                          {formatDate(questionnaire.finished_at)}
                        </ChineseText>
                      </HStack>
                    )}

                    <HStack>
                      <ChineseText fontSize="14px" color="#666666" fontFamily="Inter" fontWeight="bold">
                        總題數：
                      </ChineseText>
                      <ChineseText fontSize="14px" color="#000000" fontFamily="Inter">
                        {answers.length} 題
                      </ChineseText>
                    </HStack>
                  </VStack>
                </GridItem>
              </Grid>
            </Card.Body>
          </Card.Root>

          {/* Questions and Answers Section */}
          <Card.Root border="1px solid #e0e0e0" borderRadius="12px">
            <Card.Header>
              <Heading size="md" color="#000000" fontFamily="Inter">
                問題與答案
              </Heading>
            </Card.Header>
            <Card.Body>
              {Object.keys(answersByCategory).length === 0 ? (
                <ChineseText fontSize="16px" color="#666666" fontFamily="Inter" textAlign="center">
                  暫無答案記錄
                </ChineseText>
              ) : (
                <Accordion.Root multiple>
                  {Object.entries(answersByCategory).map(([category, categoryAnswers]) => (
                    <Accordion.Item key={category} value={category} border="1px solid #e0e0e0" borderRadius="8px" mb={4}>
                      <Accordion.ItemTrigger _hover={{ bg: "rgba(0,0,0,0.02)" }}>
                        <Box flex="1" textAlign="left">
                          <ChineseText fontSize="18px" fontWeight="bold" color="#000000" fontFamily="Inter">
                            {category} ({categoryAnswers.length} 題)
                          </ChineseText>
                        </Box>
                        <Accordion.ItemIndicator />
                      </Accordion.ItemTrigger>
                      <Accordion.ItemContent>
                        <Accordion.ItemBody pb={4}>
                        <VStack gap={4} align="stretch">
                          {categoryAnswers.map((answer: any, index: number) => (
                            <Box key={index} p={4} border="1px solid #f0f0f0" borderRadius="8px" bg="#fafafa">
                              <VStack align="flex-start" gap={3}>
                                <ChineseText fontSize="16px" fontWeight="bold" color="#000000" fontFamily="Inter">
                                  問題 {answer.question_id}
                                </ChineseText>
                                
                                <ChineseText fontSize="14px" color="#333333" fontFamily="Inter" lineHeight="1.6">
                                  {answer.question_text}
                                </ChineseText>

                                <Separator />

                                <HStack align="flex-start" gap={2}>
                                  <ChineseText fontSize="14px" color="#666666" fontFamily="Inter" fontWeight="bold">
                                    核心能力：
                                  </ChineseText>
                                  <ChineseText fontSize="14px" color="#000000" fontFamily="Inter">
                                    {answer.core_competency_name}
                                  </ChineseText>
                                </HStack>

                                <HStack align="flex-start" gap={2}>
                                  <ChineseText fontSize="14px" color="#666666" fontFamily="Inter" fontWeight="bold">
                                    選擇答案：
                                  </ChineseText>
                                  <Badge colorScheme="blue" fontSize="12px">
                                    {answer.selected_answer}
                                  </Badge>
                                </HStack>

                                <Box pl={4} borderLeft="3px solid #d3401f">
                                  <ChineseText fontSize="14px" color="#333333" fontFamily="Inter" lineHeight="1.5">
                                    {getAnswerLabel(answer.selected_answer)}
                                  </ChineseText>
                                </Box>
                              </VStack>
                            </Box>
                          ))}
                        </VStack>
                        </Accordion.ItemBody>
                      </Accordion.ItemContent>
                    </Accordion.Item>
                  ))}
                </Accordion.Root>
              )}
            </Card.Body>
          </Card.Root>

          {/* A4 Report Section */}
          <Card.Root border="1px solid #e0e0e0" borderRadius="12px">
            <Card.Header>
              <Heading size="md" color="#000000" fontFamily="Inter">
                評估報告
              </Heading>
            </Card.Header>
            <Card.Body>
              <A4Box>
                <VStack gap={6} align="stretch">
                  {/* Report content will be developed later */}
                  <Box textAlign="center" py={20}>
                    <ChineseText fontSize="18px" color="#666666" fontFamily="Inter">
                      報告內容開發中...
                    </ChineseText>
                  </Box>
                </VStack>
              </A4Box>
            </Card.Body>
          </Card.Root>

          {/* Action Buttons */}
          <HStack justify="center" gap={4} pt={4}>
            <Button
              bg="#d3401f"
              color="#ffffff"
              borderRadius="8px"
              h="48px"
              px="32px"
              fontSize="18px"
              fontWeight="600"
              fontFamily="Inter"
              onClick={() => window.print()}
              _hover={{ bg: "#b8351a" }}
            >
              📄 列印詳情
            </Button>
            <Button
              bg="#2196F3"
              color="#ffffff"
              borderRadius="8px"
              h="48px"
              px="32px"
              fontSize="18px"
              fontWeight="600"
              fontFamily="Inter"
              onClick={() => navigate({ to: "/questionnaire-history" })}
              _hover={{ bg: "#1976D2" }}
            >
              返回記錄
            </Button>
          </HStack>
        </VStack>
      </Box>
    </Box>
  )
}
